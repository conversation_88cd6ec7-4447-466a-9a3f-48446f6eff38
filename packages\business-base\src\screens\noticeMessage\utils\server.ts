// import { PointPlan } from '@xlb/business-kms/src/screens/businessDistrict/type'
import { BusinessDistrict } from '@xlb/business-kms/src/screens/businessDistrict/type'
import { PointPlan } from '@xlb/business-kms/src/screens/pointPlan/type'
import { KmsHttp } from '@xlb/common/src/services/lib/kmshttp'
import { InterestUser, NoticeItem } from './type'

class NoticeServer {
  protected server = KmsHttp

  /** 获取点位详情 */
  getPointDetail = async (params: Partial<PointPlan>) => {
    const { data } = await this.server.post<Result<PointPlan>, Partial<PointPlan>>('/kms/hxl.kms.storeplan.read', params)
    return data
  }

  /** 获取客户管理详情 */
  getClientManageDetail = async (params: Partial<PointPlan>) => {
    const { data } = await this.server.post<Result<PointPlan>, Partial<PointPlan>>('/kms/hxl.kms.client.read', params)
    return data
  }

  /** 获取线索详情 */
  getClueDetail = async (params: Partial<PointPlan>) => {
    const { data } = await this.server.post<Result<PointPlan>, Partial<PointPlan>>('/kms/hxl.kms.clientclew.read', params)
    return data
  }

  /** 获取商圈详情 */
  getBusinessDetail = async (params: Partial<BusinessDistrict>) => {
    const { data } = await this.server.post<Result<BusinessDistrict>, Partial<BusinessDistrict>>('/kms/hxl.kms.businessplan.read', params)
    return data
  }

  /** 获取关闭和待施工详情 */
  getBuildTaskDetail = async (params: Partial<any>) => {
    const { data } = await this.server.post<Result<any>, Partial<any>>('/kms/hxl.kms.buildtask.read', params)
    return data
  }

  /** 关注 */
  focus = async (params: Partial<InterestUser>) => {
    const data = await this.server.post<Result<InterestUser>, Partial<InterestUser>>('/kms/hxl.kms.interestuser.save', params)
    return data
  }

  /** 取消关注 */
  unFocus = async (params: Partial<InterestUser>) => {
    const data = await this.server.post<Result<InterestUser>, Partial<InterestUser>>('/kms/hxl.kms.interestuser.delete', params)
    return data
  }

  /** 获取关注人列表 */
  getFocusLists = async (params: Partial<InterestUser>) => {
    const { data } = await this.server.post<Result<InterestUser[]>, Partial<InterestUser>>('/kms/hxl.kms.interestuser.find', params)
    return data
  }

  /** 未读条数 */
  getNotReadLists = async (params: Partial<InterestUser>) => {
    const data = await this.server.post<Result<number>, Partial<InterestUser>>('/kms/hxl.kms.noticerecord.notread.find', params)
    return data
  }

  /** 获取通知数据 */
  getNoticeLists = async (params: BaseQuery) => {
    const data = await this.server.post<Result<NoticeItem[]>, Partial<InterestUser>>('/kms/hxl.kms.noticerecord.find', params)
    return data
  }

  /** 是否关注 */
  getIsFocused = async (params: Partial<InterestUser>) => {
    const data = await this.server.post<Result<{ flag: boolean }>, Partial<InterestUser>>('/kms/hxl.kms.interestuser.flag.find', params)
    return data
  }

  /** 浏览记录 */
  readRecord = async (params: Partial<InterestUser>) => {
    const data = await this.server.post<Result<{ flag: boolean }>, Partial<InterestUser>>('/kms/hxl.kms.noticerecord.update', params)
    return data
  }

  /** 门店用户公告 */
  USER = async () => {
    const data = await this.server.post('/erp/hxl.erp.usernotice.page', { read: false })
    return data
  }
  /** 门店用户公告 */
  SUPPLIER = async () => {
    const data = await this.server.post('/scm/hxl.scm.usernotice.page', { read: false })
    return data
  }
  /** 门店用户公告 */
  USERREAD = async (params: any) => {
    const data = await this.server.post('/erp/hxl.erp.usernotice.read', params)
    return data
  }
  /** 门店用户公告 */
  SUPPLIERREAD = async (params: any) => {
    const data = await this.server.post('/scm/hxl.scm.usernotice.read', params)
    return data
  }
}

export default new NoticeServer()
