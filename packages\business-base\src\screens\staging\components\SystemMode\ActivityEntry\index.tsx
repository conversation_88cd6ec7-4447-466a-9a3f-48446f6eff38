import React, { useMemo } from 'react'
import { Image, TouchableOpacity, StyleSheet, Dimensions, ImageBackground, View } from 'react-native'
// import { ErpRoutes } from '@xlb/business-erp/src/config/route'
import { useRequest } from 'ahooks'
import { ErpHttp } from '@xlb/common/src/services/lib/erphttpnew'
import dayjs from 'dayjs'

interface ActivityEntryProps {
  goTo: (route: string, params?: Record<string, any>) => void
  callback?: (num: number) => void
}

// 活动图片地址
const ACTIVITY_IMAGE_URL = require('@xlb/business-base/src/screens/staging/components/SystemMode/ActivityEntry/images/spring_festival_banner.png')

const { width: SCREEN_WIDTH } = Dimensions.get('window')

const ActivityEntry: React.FC<ActivityEntryProps> = (props) => {
  const { goTo, callback } = props

  // 是否活动期间
  const getActivityStatus = async () => {
    const res: CommonResponse = await ErpHttp.post('/erp/hxl.erp.prepare.appentrance')
    return res.data
  }
  const { data: activityStatus } = useRequest(getActivityStatus)

  // 检查活动是否应该展示
  const isActivity = useMemo(() => {
    return !!activityStatus?.is_work
  }, [activityStatus])

  // 处理活动点击
  const handleActivityPress = () => {
    if (!isActivity) return

    goTo(ErpRoutes.StoreReplenishment, {
      activityEntry: true, // 添加活动入口标识
    })
  }

  // 如果没有可用活动，不渲染任何内容
  if (!isActivity) return null

  const handleLayout = (event) => {
    const { height } = event.nativeEvent.layout
    callback && callback(height)
  }

  return (
    <View onLayout={handleLayout}>
    <TouchableOpacity style={styles.container} onPress={handleActivityPress} activeOpacity={0.9}>
      <ImageBackground source={activityStatus?.url ? { uri: activityStatus.url } : ACTIVITY_IMAGE_URL} style={styles.image} resizeMode="contain" />
    </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginTop: 4,
  },
  image: {
    width: '100%',
    height: (SCREEN_WIDTH - 12) * 0.18,
    overflow: 'hidden',
  },
})

export default ActivityEntry
