import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'

// 产品销售分析详情
// const productSalesAnalysisDetails = (params: any) => {
//   return ErpHttp.post<CommonResponse>('/erp/hxl.erp.app.posreport.find.rank', params, {
//     timeout: 120000,
//   })
// }

// 商品销售分析
const productSalesAnalysis = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.itemanalyse.find', params, {
    timeout: 120000,
  })
}

// 单品销售业绩
const singleProduct = (params: { item_id: number; store_id: number }) => {
  return ErpHttp.post<CommonResponse>('/erp/hxl.erp.app.itemanalyse.read', params)
}

export const storeSaleApi = {
  productSalesAnalysis,
  singleProduct,
  // productSalesAnalysisDetails,
}
