import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'

// 查询轮播图
export const getAppTodo = (params: any) => {
  return ErpHttp.post('/erp/hxl.erp.apptodo.find', params)
}

// 云学堂对接
export const getYunXueTangAuthUrl = (params: any) => {
  return ErpHttp.post('/hrs/hxl.hrs.yunxuetang.authurl', { ...params })
}

type pageDiyParamsType = {
  position_id: string
}
/**
 * 获取页面角色diy
 * @param params
 * @returns
 */
export const getPagediy = (params: pageDiyParamsType) => {
  return ErpHttp.post('/mdm/page.diy.read', {
    ...params,
  })
}

/**
 * 获取页面角色diy
 * @param params
 * @returns
 */
export const getAccountPagediy = () => {
  return ErpHttp.post(
    '/mdm/account.page.diy.read',
    {},
    {
      isHiddenMsg: true,
    }
  )
}

/**
 * 更新页面角色diy
 * @param params
 * @returns
 */
export const getAccountPageUpdate = (params: { value: any }) => {
  return ErpHttp.post('/mdm/account.page.diy.update', {
    ...params,
  })
}
