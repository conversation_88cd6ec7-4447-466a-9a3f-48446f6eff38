/*
 * @Author: PengKang <EMAIL>
 * @Date: 2024-11-21 10:36:48
 * @LastEditors: PengKang <EMAIL>
 * @LastEditTime: 2024-11-23 14:18:38
 * @FilePath: \xlb_app\packages\business-base\src\screens\staging\server.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'

// 查询轮播图
export const getSwipers = (params: any) => {
  return ErpHttp.post('/erp/hxl.erp.app.loopimage.find', params)
}

// 查询快速访问应用
export const getAccessApps = (params: any) => {
  return ErpHttp.post('/erp/hxl.erp.app.bookmark.find', params)
}

// 增加快速访问应用
export const addAccessApps = (params: any) => {
  return ErpHttp.post('/erp/hxl.erp.app.bookmark.batchsave', params)
}

// 删除快速访问应用
export const deleteAccessApps = (params: any) => {
  return ErpHttp.post('/erp/hxl.erp.app.bookmark.batchdelete', params)
}

//kms待办事项
export const getKMSWaitHandle = () => {
  return ErpHttp.post('/kms/hxl.kms.join.waithandle.find')
}
//wms待办事项
export const getWMSWaitHandle = (params: any) => {
  return ErpHttp.post('/wms/hxl.wms.waithandle.find', params)
}

//erp ---发车待办
export const getErpDepartWaitHandle = (params: any) => {
  return ErpHttp.post('/wms/hxl.wms.departurecenter.store.find', params)
}

//erp ---待办
export const getErpWaitHandle = (params: any) => {
  return ErpHttp.post('/erp/hxl.erp.homepage.waithandle.app.find', params)
}
//scm ---待办
export const getScmWaitHandle = (params: any) => {
  return ErpHttp.post('/scm/hxl.scm.homepage.waithandle.find', params, { timeout: 20000 })
}
//fsms ---门店证件待办
export const getFsmsWaitHandle = (params: any) => {
  return ErpHttp.post('/fsms/hxl.fsms.storefilemanage.reminder', params)
}

//ems --- 工单管理
export const getWorkOrderList = (params: any) => {
  return ErpHttp.post('/ems/hxl.ems.maintenanceorder.count', params)
}
//ems --- 代办
export const getEmsWaitHandle = () => {
  // return ErpHttp.post('/ems/hxl.ems.waithandle.find')
  return ErpHttp.post('/ems/hxl.ems.waithandle.web.find')
}
// ems --- 代办设为已读
export const updateEmsWaitHandle = (params: any) => {
  return ErpHttp.post('/ems/hxl.ems.waithandle.update', params)
}

//巡店任务代办 ---demo
export const getShopPatrolTask = (params: any = {}) => {
  return ErpHttp.post('/sms/hxl.sms.taskitem.appwait.count', params);
}
